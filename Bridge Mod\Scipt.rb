#===============================================================================
# Bridge-Aware NPCs Script
# Automatically adjusts NPC behavior based on bridge level
#===============================================================================

module BridgeAwareNPCs
  # Constants for z-level adjustments
  BRIDGE_HEIGHT_OFFSET = 64  # Pixels above bridge level
  UNDER_BRIDGE_Z = -32       # Z-level for NPCs under bridges
  
  # Check if an event has bridge-aware naming
  def self.is_bridge_aware?(event)
    return false if !event || !event.name
    name = event.name.downcase
    # Check for z-level naming patterns
    return true if name.include?("z100") && name.include?("z0")
    return true if name.include?("onz") && name.include?("offz")
    return false
  end
  
  # Determine if NPC should be "on" the bridge based on naming
  def self.is_on_bridge_npc?(event)
    return false if !event || !event.name
    name = event.name.downcase
    # NPCs with z100 or OnZ are considered "on bridge" NPCs
    return true if name.include?("z100") || name.include?("onz")
    return false
  end
  
  # Determine if NPC should be "under" the bridge based on naming
  def self.is_under_bridge_npc?(event)
    return false if !event || !event.name
    name = event.name.downcase
    # NPCs with z0 or OffZ are considered "under bridge" NPCs
    return true if name.include?("z0") || name.include?("offz")
    return false
  end
  
  # Update NPC properties based on bridge state
  def self.update_npc_for_bridge(event)
    return if !is_bridge_aware?(event)
    
    bridge_active = $PokemonGlobal.bridge > 0
    
    if is_on_bridge_npc?(event)
      # NPC that should be on the bridge
      if bridge_active
        # Bridge is on - NPC is interactable and above bridge
        event.through = false
        event.instance_variable_set(:@bridge_z_offset, BRIDGE_HEIGHT_OFFSET)
      else
        # Bridge is off - NPC is non-interactable and walkable
        event.through = true
        event.instance_variable_set(:@bridge_z_offset, 0)
      end
    elsif is_under_bridge_npc?(event)
      # NPC that should be under the bridge
      if bridge_active
        # Bridge is on - NPC is non-interactable and walkable
        event.through = true
        event.instance_variable_set(:@bridge_z_offset, UNDER_BRIDGE_Z)
      else
        # Bridge is off - NPC is interactable and normal z-level
        event.through = false
        event.instance_variable_set(:@bridge_z_offset, 0)
      end
    end
  end
  
  # Refresh all bridge-aware NPCs on the current map
  def self.refresh_all_npcs
    return if !$game_map
    $game_map.events.each_value do |event|
      next if !is_bridge_aware?(event)
      update_npc_for_bridge(event)
    end
  end
end

#===============================================================================
# Override pbBridgeOn to refresh NPCs
#===============================================================================
alias __bridge_npcs_pbBridgeOn pbBridgeOn
def pbBridgeOn(height = 2)
  __bridge_npcs_pbBridgeOn(height)
  BridgeAwareNPCs.refresh_all_npcs
end

#===============================================================================
# Override pbBridgeOff to refresh NPCs
#===============================================================================
alias __bridge_npcs_pbBridgeOff pbBridgeOff
def pbBridgeOff
  __bridge_npcs_pbBridgeOff
  BridgeAwareNPCs.refresh_all_npcs
end

#===============================================================================
# Extend Game_Event to handle bridge-aware z-levels
#===============================================================================
class Game_Event
  alias __bridge_npcs_screen_z screen_z
  def screen_z(height = 0)
    base_z = __bridge_npcs_screen_z(height)
    
    # Apply bridge z-offset if this is a bridge-aware NPC
    if BridgeAwareNPCs.is_bridge_aware?(self)
      bridge_offset = @bridge_z_offset || 0
      return base_z + bridge_offset
    end
    
    return base_z
  end
  
  # Override refresh to update bridge behavior
  alias __bridge_npcs_refresh refresh
  def refresh
    __bridge_npcs_refresh
    BridgeAwareNPCs.update_npc_for_bridge(self)
  end
end

#===============================================================================
# Extend Game_Character to handle proper z-ordering for bridge NPCs
#===============================================================================
class Game_Character
  alias __bridge_npcs_screen_z screen_z
  def screen_z(height = 0)
    base_z = __bridge_npcs_screen_z(height)
    
    # Special handling for bridge-aware events
    if self.is_a?(Game_Event) && BridgeAwareNPCs.is_bridge_aware?(self)
      bridge_offset = @bridge_z_offset || 0
      
      # For "on bridge" NPCs when bridge is active, ensure they render above player
      if BridgeAwareNPCs.is_on_bridge_npc?(self) && $PokemonGlobal.bridge > 0
        # Add extra height to ensure proper layering above player and bridge
        return base_z + bridge_offset + 32
      else
        return base_z + bridge_offset
      end
    end
    
    return base_z
  end
end

#===============================================================================
# Initialize bridge-aware NPCs when map loads
#===============================================================================
EventHandlers.add(:on_enter_map, :refresh_bridge_npcs,
  proc {
    BridgeAwareNPCs.refresh_all_npcs
  }
)

#===============================================================================
# Debug function to manually refresh bridge NPCs (optional)
#===============================================================================
def pbRefreshBridgeNPCs
  BridgeAwareNPCs.refresh_all_npcs
  pbMessage("Bridge-aware NPCs refreshed!")
end
